<?php

namespace App\Services\Access;

use App\DTOs\Xui\InboundDTO;
use App\Models\User;
use App\Models\Announce;
use App\Models\Setting;
use App\Models\SniEndpoint;
use App\Services\HelperService;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;
use App\Models\XuiServer;
use App\Services\ServerInfoService;
use App\Services\Traffic\TrafficLoggerService;

class AccessService
{
    public function __construct(
        private HelperService $helperService
    ) {}

    /**
     * Generate VLESS keys for a user based on their active subscription and available servers.
     *
     * @var XuiServer $server
     * @var InboundDTO $inbound
     */
    public function generateVlessKeys(User $user): string
    {
        // Get user's active subscription
        $subscription = $user->currentSubscription;
        if (!$subscription || !$subscription->isActive()) {
            Log::info("No active subscription found for user {$user->id}");
            return '';
        }

        // Get server pool from user's active server pools
        $serverPools = $user->serverPools()
                ->where('server_pools.is_active', true)
                ->whereNull('user_server_assignments.released_at')
                ->with('servers')
                ->get();

        if ($serverPools->isEmpty()) {
            Log::warning("No active server pools found for user {$user->id}");
            return '';
        }

        // Use the first active server pool
        /** @var ServerPool $serverPool */
        $serverPool = $serverPools->first();

        // Get active servers in the pool
        $servers = $serverPool->activeServers()->get();
        if ($servers->isEmpty()) {
            Log::warning("No active servers found in pool {$serverPool->id}");
            return '';
        }

        $vlessLinks = [];

        foreach ($servers as $server) {
            /** @var XuiServer $server */
            // Get inbounds for this server
            $inbounds = $server->inbounds();

            foreach ($inbounds as $inbound) {
                /** @var InboundDTO $inbound */

                // skip if remark contains test word or starts with dot
                if (str_contains($inbound->remark, 'test') || str_starts_with($inbound->remark, '.')) {
                    // continue;
                }

                // skip if remark is not valid for xhttp
                // if ($inbound->streamSettings['network'] === 'xhttp' && ! $this->parseRemarkForXhttp($inbound->remark)) {
                //     continue;
                // }

                // skip if inbound is not enabled
                if (!$inbound->enable) {
                    continue;
                }

                try {
                    $vlessUrl = $this->generateVlessUrl($user, $server, $inbound);
                    if ($vlessUrl) {
                        $vlessLinks[] = $vlessUrl;
                    }
                } catch (\Exception $e) {
                    Log::error("Error generating VLESS URL for user {$user->id}", [
                        'server' => $server->name,
                        'inbound' => $inbound->remark,
                        'error' => $e->getMessage(),
                    ]);
                }
            }
        }

        if (empty($vlessLinks)) {
            Log::warning("No VLESS links generated for user {$user->id}");
            return '';
        }

        return implode("\n", $vlessLinks);
    }

    public function generateVlessUrlForInbound(User $user, XuiServer $server, InboundDTO $inbound): string
    {
        /**
        // js code from 3x-ui panel, this method generates correct vless link from any inbound settings. Need to convert it to php
        genVLESSLink(address = '', port = this.port, forceTls, remark = '', clientId, flow) {
                const uuid = clientId;
                const type = this.stream.network;
                const security = forceTls == 'same' ? this.stream.security : forceTls;
                const params = new Map();
                params.set("type", this.stream.network);
                params.set("encryption", this.settings.encryption);
                switch (type) {
                    case "tcp":
                        const tcp = this.stream.tcp;
                        if (tcp.type === 'http') {
                            const request = tcp.request;
                            params.set("path", request.path.join(','));
                            const index = request.headers.findIndex(header => header.name.toLowerCase() === 'host');
                            if (index >= 0) {
                                const host = request.headers[index].value;
                                params.set("host", host);
                            }
                            params.set("headerType", 'http');
                        }
                        break;
                    case "kcp":
                        const kcp = this.stream.kcp;
                        break;
                    case "ws":
                        const ws = this.stream.ws;
                        params.set("path", ws.path);
                        params.set("host", ws.host?.length > 0 ? ws.host : this.getHeader(ws, 'host'));
                        break;
                    case "grpc":
                        const grpc = this.stream.grpc;
                        params.set("serviceName", grpc.serviceName);
                        params.set("authority", grpc.authority);
                        if (grpc.multiMode) {
                            params.set("mode", "multi");
                        }
                        break;
                    case "httpupgrade":
                        const httpupgrade = this.stream.httpupgrade;
                        params.set("path", httpupgrade.path);
                        params.set("host", httpupgrade.host?.length > 0 ? httpupgrade.host : this.getHeader(httpupgrade, 'host'));
                        break;
                    case "xhttp":
                        const xhttp = this.stream.xhttp;
                        params.set("path", xhttp.path);
                        params.set("host", xhttp.host?.length > 0 ? xhttp.host : this.getHeader(xhttp, 'host'));
                        params.set("mode", xhttp.mode);
                        break;
                }

                if (security === 'tls') {
                    params.set("security", "tls");
                    if (this.stream.isTls) {
                        params.set("fp", this.stream.tls.settings.fingerprint);
                        params.set("alpn", this.stream.tls.alpn);
                        if (!ObjectUtil.isEmpty(this.stream.tls.sni)) {
                            params.set("sni", this.stream.tls.sni);
                        }
                        if (this.stream.tls.settings.echConfigList?.length > 0) {
                            params.set("ech", this.stream.tls.settings.echConfigList);
                        }
                        if (type == "tcp" && !ObjectUtil.isEmpty(flow)) {
                            params.set("flow", flow);
                        }
                    }
                }

                else if (security === 'reality') {
                    params.set("security", "reality");
                    params.set("pbk", this.stream.reality.settings.publicKey);
                    params.set("fp", this.stream.reality.settings.fingerprint);
                    if (!ObjectUtil.isArrEmpty(this.stream.reality.serverNames)) {
                        params.set("sni", this.stream.reality.serverNames.split(",")[0]);
                    }
                    if (this.stream.reality.shortIds.length > 0) {
                        params.set("sid", this.stream.reality.shortIds.split(",")[0]);
                    }
                    if (!ObjectUtil.isEmpty(this.stream.reality.settings.spiderX)) {
                        params.set("spx", this.stream.reality.settings.spiderX);
                    }
                    if (!ObjectUtil.isEmpty(this.stream.reality.settings.mldsa65Verify)) {
                        params.set("pqv", this.stream.reality.settings.mldsa65Verify);
                    }
                    if (type == 'tcp' && !ObjectUtil.isEmpty(flow)) {
                        params.set("flow", flow);
                    }
                }

                else {
                    params.set("security", "none");
                }

                const link = `vless://${uuid}@${address}:${port}`;
                const url = new URL(link);
                for (const [key, value] of params) {
                    url.searchParams.set(key, value)
                }
                url.hash = encodeURIComponent(remark);
                return url.toString();
            }
        */

    }

    private function genVLESSLink(InboundDTO $stream, $address = '', $port = null, $forceTls, $remark = '', $clientId, $flow)
    {
        // js code from 3x-ui panel, this method generates correct vless link from any inbound settings
        // Need to convert it to php
        $uuid = $clientId;
        $type = $stream->network;
    }

    /**
     * Generate a single VLESS URL for user, server, and inbound.
     */
    private function generateVlessUrl(User $user, XuiServer $server, InboundDTO $inbound): string
    {
        /*
        streamSettings:
        {
"network":"xhttp",
"security":"tls",
"externalProxy":[
{
"forceTls":"same",
"dest":"gw4.smartvpn.vip",
"port":8443,
"remark":""
}
],
"tlsSettings":{
"serverName":"gw4.smartvpn.vip",
"minVersion":"1.2",
"maxVersion":"1.3",
"cipherSuites":"",
"rejectUnknownSni":false,
"disableSystemRoot":false,
"enableSessionResumption":false,
"certificates":[
{
"certificateFile":"/root/cert/gw4.smartvpn.vip/fullchain.pem",
"keyFile":"/root/cert/gw4.smartvpn.vip/privkey.pem",
"oneTimeLoading":false,
"usage":"encipherment",
"buildChain":false
}
],
"alpn":[
"h2",
"http/1.1",
"h3"
],
"echServerKeys":"",
"echForceQuery":"none",
"settings":{
"fingerprint":"chrome",
"echConfigList":""
}
},
"xhttpSettings":{
"path":"/",
"host":"gw4.smartvpn.vip",
"headers":{
},
"scMaxBufferedPosts":30,
"scMaxEachPostBytes":"1000000",
"scStreamUpServerSecs":"20-80",
"noSSEHeader":false,
"xPaddingBytes":"100-1000",
"mode":"auto",
"xPaddingObfsMode":false,
"xPaddingKey":"",
"xPaddingHeader":"",
"xPaddingPlacement":"",
"xPaddingMethod":"",
"uplinkHTTPMethod":"",
"sessionPlacement":"",
"sessionKey":"",
"seqPlacement":"",
"seqKey":"",
"uplinkDataPlacement":"",
"uplinkDataKey":"",
"uplinkChunkSize":0
},
"sockopt":{
"acceptProxyProtocol":false,
"tcpFastOpen":true,
"mark":0,
"tproxy":"off",
"tcpMptcp":false,
"penetrate":false,
"domainStrategy":"UseIP",
"tcpMaxSeg":1440,
"dialerProxy":"",
"tcpKeepAliveInterval":0,
"tcpKeepAliveIdle":300,
"tcpUserTimeout":10000,
"tcpcongestion":"bbr",
"V6Only":false,
"tcpWindowClamp":600,
"interface":""
}
}
при такой настройки vless ссылка будет такой
vless://<EMAIL>:8443?type=xhttp&encryption=none&path=%2F&host=gw4.smartvpn.vip&mode=auto&security=tls&fp=chrome&alpn=h2%2Chttp%2F1.1%2Ch3&sni=gw4.smartvpn.vip#
        */
        $userUuid = $user->id;
        $streamSettings = $inbound->streamSettings;

        // For xhttp protocol, use domain and port from remark
        if (isset($streamSettings['network']) && $streamSettings['network'] === 'xhttp') {
            // var_dump($streamSettings);
            $remarkData = $this->parseRemarkForXhttp($inbound->remark);

            if ($remarkData) {
                $serverAddress = $remarkData['domain'];
                $serverPort = $remarkData['port'];
                $cleanRemark = $remarkData['clean_remark'];
            } else {
                if (count($streamSettings['externalProxy']) > 0) {
                    $serverAddress = $streamSettings['externalProxy'][0]['dest'];
                    $serverPort = $streamSettings['externalProxy'][0]['port'];
                }
                $cleanRemark = $inbound->remark;
            }

        } else {
            // For non-xhttp protocols, use server data
            $serverAddress = $server->address;
            $serverPort = $inbound->port;
            $cleanRemark = $inbound->remark;
        }

        // Build query parameters from inbound stream settings
        $queryParams = $this->buildQueryParamsFromStreamSettings($inbound->streamSettings, $user, $inbound);
        if ($inbound->id === 12) {
            dd($queryParams);
        }
        // Build remark with server load info
        $customString = ' ' . app(ServerInfoService::class)->toBrailleLoadChar($server->server_load); //$this->getServerLoadString($server);

        $remark = $cleanRemark . $customString;
        $remark = str_replace(' ', ' ', $remark);
        $remark = urlencode($remark);

        // Construct VLESS URL
        $vlessUrl = "vless://{$userUuid}@{$serverAddress}:{$serverPort}";


        if (!empty($queryParams)) {
            $vlessUrl .= '?' . http_build_query($queryParams);
        }

        $vlessUrl .= "#{$remark}";

        // var_dump($vlessUrl);
        return $vlessUrl;
    }

    private function getSniForUser(User $user)
    {
        // Determine SNI category based on user preference
        $sniCategoryName = $user->sni_category ?? ($user->isSniMessenger() ? 'messengers' : 'russian_local');

        // Get optimal SNI endpoint for the user's category
        $endpoint = SniEndpoint::enabled()
            ->accessible()
            ->whereHas('provider.category', function ($q) use ($sniCategoryName) {
                $q->where('name', $sniCategoryName)
                  ->where('enabled', true);
            })
            ->whereHas('provider', function ($q) {
                $q->where('enabled', true);
            })
            ->whereHas('sniTags', function ($q) {
                $q->where('name', 'optimal');
            })
            ->orderBy('priority')
            ->orderBy('ping')
            ->first();

        if ($endpoint) {
            // Increment usage counter
            // $endpoint->incrementUsage();
            return $endpoint->domain;
        }

        // Fallback: get any enabled endpoint from the category
        $fallbackEndpoint = $this->getOptimalSni();
        if ($fallbackEndpoint) {
            // $fallbackEndpoint->incrementUsage();
            return $fallbackEndpoint->domain;
        }

        // Last fallback: any enabled endpoint
        $lastFallback = SniEndpoint::enabled()
            ->accessible()
            ->whereHas('provider', function ($q) {
                $q->where('enabled', true);
            })
            ->orderBy('priority')
            ->orderBy('ping')
            ->first();

        if ($lastFallback) {
            // $lastFallback->incrementUsage();
            return $lastFallback->domain;
        }

        return null;
    }


    private function getOptimalSni()
    {
        $fallbackEndpoint = SniEndpoint::enabled()
            ->accessible()
            ->whereHas('sniTags', function ($q) {
                $q->where('name', 'optimal');
            })
            ->whereHas('provider', function ($q) {
                $q->where('enabled', true);
            })
            ->orderBy('priority')
            ->orderBy('ping')
            ->first();

        if ($fallbackEndpoint) {
            // $fallbackEndpoint->incrementUsage();
            return $fallbackEndpoint->domain;
        }
    }

    /**
     * Build query parameters from inbound stream settings.
     */

    private function buildQueryParamsFromStreamSettings(array $streamSettings, User $user, InboundDTO $inbound): array
    {
        $params = [];

        // Network type
        if (isset($streamSettings['network'])) {
            $params['type'] = $streamSettings['network'];
        }

        // Handle xhttp protocol
        if (isset($streamSettings['network']) && $streamSettings['network'] === 'xhttp') {
            if ($inbound->id === 12) {
                dd($streamSettings, $this->buildXhttpParams($streamSettings, $user, $inbound));
            }
            return $this->buildXhttpParams($streamSettings, $user, $inbound);
        }

        // Security
        if (isset($streamSettings['security'])) {
            $params['security'] = $streamSettings['security'];
        }

        if (isset($streamSettings['tlsSettings'])) {
            $tlsSettings = $streamSettings['tlsSettings'];

            if (isset($tlsSettings['settings']['fingerprint'])) {
                $params['fp'] = $tlsSettings['settings']['fingerprint'];
            }

            if (isset($tlsSettings['serverName'])) {
                $params['sni'] = $tlsSettings['serverName'];
            }

            if (isset($tlsSettings['settings']['allowInsecure'])) {
                $params['allowInsecure'] = $tlsSettings['settings']['allowInsecure'] ? '1' : '0';
            }

            if (isset($tlsSettings['alpn'])) {
                $params['alpn'] = implode(',', $tlsSettings['alpn']);
            }
        }

        // Reality settings
        if (isset($streamSettings['realitySettings'])) {
            $reality = $streamSettings['realitySettings'];

            if (isset($reality['settings']['publicKey'])) {
                $params['pbk'] = $reality['settings']['publicKey'];
            }

            if (isset($reality['settings']['fingerprint'])) {
                $params['fp'] = $reality['settings']['fingerprint'];
            }

            if (isset($reality['serverNames']) && !empty($reality['serverNames'])) {
                $params['sni'] = $this->getOptimalSni() ?? $reality['serverNames'][0];
            }

            if (isset($reality['shortIds'])) {
                $params['sid'] = $reality['shortIds'][0];
            }

            if (isset($reality['settings']['spiderX'])) {
                $params['spx'] = $reality['settings']['spiderX'];
            }

            if (isset($reality['settings']['mldsa65Verify'])) {
                $params['mldsa65Verify'] = $reality['settings']['mldsa65Verify'];
            }

        }

        // Flow (for XTLS)
        // $flow = Setting::get('default_flow', 'xtls-rprx-vision');
        // Switch flow on for reality only
        $flow = 'xtls-rprx-vision-udp443';
        if ($streamSettings['security'] === 'reality' && $flow) {
            $params['flow'] = $flow;
        }

        return $params;
    }

    /**
     * Build query parameters for xhttp protocol.
     * Expected format: mode=auto&path=%2F&security=tls&alpn=http%2F1.1&encryption=none&fp={fingerprint}&type=xhttp&sni={domain}
     */
    private function buildXhttpParams(array $streamSettings, User $user, InboundDTO $inbound): array
    {
        $params = [];
        $params['type'] = 'xhttp';

        // Parse remark to get domain, port, and fingerprint
        $remarkData = $this->parseRemarkForXhttp($inbound->remark);

        if (! $remarkData) {
            $streamSettings = $inbound->streamSettings;
            $tlsSettings = $streamSettings['tlsSettings'] ?? null;
            $xhttpSettings = $streamSettings['xhttpSettings'] ?? null;
            $realitySettings = $streamSettings['realitySettings'] ?? null;

            $params['mode'] = 'auto';
            $params['host'] = $xhttpSettings['host'];
            $params['path'] = $xhttpSettings['path'];
            $params['security'] = $streamSettings['security'];
            if ($tlsSettings) {
                $params['alpn'] = implode(',', $tlsSettings['alpn']);
                $params['encryption'] = 'none';
                $params['fp'] = $tlsSettings['settings']['fingerprint'];
                $params['sni'] = $streamSettings['security'] === 'tls' ? $streamSettings['tlsSettings']['serverName'] : '';
                $params['allowInsecure'] = $streamSettings['tlsSettings']['settings']['allowInsecure'] ? '1' : '0';
            }
            if ($realitySettings) {
                $params['pbk'] = $realitySettings['settings']['publicKey'];
                $params['fp'] = $realitySettings['settings']['fingerprint'];
                $params['sni'] = $realitySettings['serverNames'][0];
                $params['sid'] = $realitySettings['shortIds'][0];
                $params['spx'] = $realitySettings['settings']['spiderX'];
            }

            return $params;
        }

        // xhttp specific parameters
        $params['mode'] = 'auto';
        $params['host'] = '';
        $params['path'] = '/';
        $params['security'] = 'tls';
        $params['alpn'] = '';
        $params['encryption'] = 'none';
        $params['fp'] = $remarkData['fingerprint'];
        $params['sni'] = $remarkData['domain'];

        return $params;
    }

    /**
     * Get server load string for display.
     */
    private function getServerLoadString(XuiServer $server): string
    {
        if ($server->server_load !== null) {
            $loadPercent = round($server->server_load, 1);
            return " ({$loadPercent}%)";
        }

        return '';
    }

    /**
     * Parse remark to extract domain, port and fingerprint for xhttp protocol.
     * Expected format: "{remark_name} ({domain}:{port}|{fingerprint})"
     * Example: "Для MTS,Beeline,Megafon,Tele2 (nn1.devet.ru:443|android)"
     *
     * @param string $remark
     * @return array|null Returns ['domain' => string, 'port' => int, 'fingerprint' => string, 'clean_remark' => string] or null if parsing fails
     */
    private function parseRemarkForXhttp(string $remark): ?array
    {
        // Pattern to match: "text (domain:port|fingerprint)"
        $pattern = '/^(.+?)\s*\(([^:]+):(\d+)\|([^)]+)\)$/';

        if (preg_match($pattern, $remark, $matches)) {
            return [
                'clean_remark' => trim($matches[1]),
                'domain' => trim($matches[2]),
                'port' => (int) $matches[3],
                'fingerprint' => '360'//trim($matches[4])
            ];
        }

        return null;
    }

    /**
     * Get routing settings for user.
     */
    public function getRoutingSettings(User $user): string
    {
        $routingRules = Setting::get('common_routing_rules', '', true);

        // If user has disabled common routing, return empty string
        if (! $user->use_common_routing) {
            return '';
        }

        // If no routing rules, return empty string
        if (empty($routingRules)) {
            return '';
        }

        // If routing rules is array, encode it to JSON
        if (is_array($routingRules)) {
            $routingRules = json_encode($routingRules);
        }

        // Encode routing rules to base64
        $routingRules = base64_encode(HelperService::encodeJsonUtf8Safe($routingRules) ?? $routingRules);

        return $routingRules;
    }

    /**
     * Get subscription headers for VPN clients.
     */
    public function getSubscriptionHeaders(User $user, string $uuid): array
    {
        $announceData = $this->getAnnounceData($user, $uuid);

        // Determine routing rules based on user preference
        $routingRules = $this->getRoutingSettings($user);

        $headers = [
            'Profile-Title' => 'base64:' . base64_encode($this->getSubscriptionTitle($user)),
            'Content-Type' => 'text/plain; charset=utf-8',
            'Profile-Update-Interval' => '1', // update interval in hours
            'Profile-Web-Page-Url' => \App\Services\HelperService::getCurrentAppUrl() . '/access/' . $uuid,
            'Support-Url' => \App\Services\HelperService::getCurrentAppUrl() . '/support/' . $uuid,
            'Announce' => $announceData['text'] ? 'base64:' . base64_encode($announceData['text']) : '',
            'Announce-Url' => $announceData['url'] ?: '',
            'Update-always' => 'true',
            'hide-settings' => 'true',
            'subscription-always-hwid-enable' => 'true',
            'subscription-auto-update-enable' => 'true',
            'subscription-auto-update-open-enable' => 'true',
            // больше не используется роутинг, нет смысла при блокировках
            // 'Routing' => $routingRules,
        ];

        // Add subscription userinfo
        $userinfo = $this->buildSubscriptionUserinfo($user);
        if ($userinfo) {
            $headers['Subscription-Userinfo'] = $userinfo;
        }

        return $headers;
    }

    /**
     * Get subscription title.
     */
    private function getSubscriptionTitle(User $user): string
    {
        $title = config("app.access.title", "SmartVPN");

        $title .= " \n       Ваш ID: {$user->public_client_id} 🔰";

        return $title;
    }

    /**
     * Get announce data based on subscription status.
     */
    private function getAnnounceData(User $user, string $uuid): array
    {
        $defaultText = config("app.access_announce", "");
        $defaultUrl = config("app.access_announce_url", "");

        try {
            $defaultUrl = HelperService::replaceTextVariables($defaultUrl, $user);

            // Get current subscription
            $currentSubscription = $user->currentSubscription;
            $earliestExpiry = $currentSubscription?->end_date;

            if ($earliestExpiry) {
                $now = Carbon::now();
                $expiryTime = Carbon::parse($earliestExpiry);

                // Check if subscription has already expired
                if ($expiryTime->isPast()) {
                    return [
                        'text' => "❗️ #c11e14ПОДПИСКА #c11e14ИСТЕКЛА ❗️\n\nНажмите сюда, чтобы продлить подписку 💳",
                        'url' => HelperService::getCurrentAppUrl() . "/access/{$uuid}/plan/select",
                    ];
                }

                // Check if expiry is within 3 days
                if ($expiryTime->isFuture() && $now->diffInDays($expiryTime, false) < 3) {
                    $remainingTime = HelperService::formatRemainingTimeShort($expiryTime, $now);

                    return [
                        'text' => "🪫 ПОДПИСКА #c11e14ЗАКОНЧИТСЯ через {$remainingTime}\n\n#c11e14Нажмите сюда, чтобы продлить подписку 💳",
                        'url' => HelperService::getCurrentAppUrl() . "/access/{$uuid}/plan/select",
                    ];
                }
            }

            // Get latest announce from database
            $latestAnnounce = Announce::getLatestVisible();

            if ($latestAnnounce) {
                $url = HelperService::replaceTextVariables($latestAnnounce->url, $user);

                return [
                    'text' => $latestAnnounce->message,
                    'url' => $url ?: $defaultUrl,
                ];
            }

            // Fallback to default
            return [
                'text' => $defaultText,
                'url' => $defaultUrl,
            ];

        } catch (\Exception $e) {
            Log::error("Error getting announce data for UUID: {$uuid}", [
                'error' => $e->getMessage(),
            ]);

            return [
                'text' => $defaultText,
                'url' => $defaultUrl,
            ];
        }
    }

    /**
     * Build subscription userinfo string.
     */
    private function buildSubscriptionUserinfo(User $user): string
    {
        /** @var Subscription $subscription */
        $subscription = $user->currentSubscription;

        $subscriptionTraffic = $subscription?->trafficDetailed;
        $totalUpload = $subscriptionTraffic['up'] ?? 0;
        $totalDownload = $subscriptionTraffic['down'] ?? 0;
        $totalLimit = $subscription?->plan?->traffic_limit_bytes ?? 0;

        // If there is no traffic consumption at all, specify at least 1 byte
        // so that the traffic scale starts to be displayed in v2ray applications
        if ($totalUpload === 0 && $totalDownload === 0) {
            $totalDownload = 1;
        }

        $userinfo = sprintf('upload=%d; download=%d', $totalUpload, $totalDownload);

        // Add total only if there are actual limits set
        if ($totalLimit > 0) {
            $userinfo .= sprintf('; total=%d', $totalLimit);
        }

        // Add expire if there's an expiry time
        if ($subscription && $subscription->end_date) {
            $expireTimestamp = $subscription->end_date->timestamp;
            $userinfo .= sprintf('; expire=%d', $expireTimestamp);
        }

        return $userinfo;
    }

    /**
     * Prepare subscription data for the view.
     */
    public function prepareSubscriptionData(User $user, string $uuid): array
    {
        $subscription = $user->currentSubscription;
        $vlessContent = $this->generateVlessKeys($user);

        // Parse vless links from content
        $vlessLinks = array_filter(explode("\n", $vlessContent));

        // Get statistics
        $totalUpload = $subscription?->trafficDetailed['up'] ?? 0;
        $totalDownload = $subscription?->trafficDetailed['down'] ?? 0;
        $totalLimit = $subscription?->plan?->traffic_limit_bytes ?? 0;

        // Determine status
        $earliestExpiry = $subscription?->end_date;
        $isExpired = $earliestExpiry && $earliestExpiry->isPast();
        $isLimited = $totalLimit > 0 && ($totalUpload + $totalDownload) >= $totalLimit;
        $status = $isLimited ? 'limited' : ($isExpired ? 'expired' : 'active');

        // Calculate remaining time
        $remainingTime = $this->calculateRemainingTime($earliestExpiry);
        // Get total traffic limit
        $trafficLimitBytes  = $subscription ? $subscription?->plan->traffic_limit_bytes : null;
        // Convert to GB
        $trafficLimitGb     = $subscription ? HelperService::formatTraffic($trafficLimitBytes) : null;
        // Get total traffic used
        $trafficUsedBytes = 0;
        // $trafficUsedBytes   = $subscription ? app(TrafficLoggerService::class)
        //                                                     ->getTotalTrafficForUser(
        //                                                             userId: $user->id,
        //                                                         subscriptionId: $subscription->id,
        //                                                     ) : null;

        // Convert to GB
        $trafficUsedGb              = HelperService::formatTraffic($trafficUsedBytes);
        // Convert to GB fraction
        $trafficUsedGbInFractions   = HelperService::toGbFraction($trafficUsedBytes);
        // Calculate usage percentage
        $trafficUsagePercentage     = HelperService::calculateTrafficUsagePercentage($trafficUsedBytes, $trafficLimitBytes);
        // Format remaining time
        $remainingFormatted         = HelperService::formatRemainingTimeShort($earliestExpiry, now());
        // Get user's plan
        $plan = $user?->plan;

        return [
            'uuid' => $uuid,
            'public_client_id' => $user->getPublicClientId(),
            'email' => $user->email ?? 'User',
            'subscription_url' => $user->access_url,
            'access_url' => $user->access_url,
            'vless_links' => $vlessLinks,
            'vless_content' => $vlessLinks[0] ?? "",
            'expiry_time' => $earliestExpiry,
            'remaining_time' => $remainingTime,
            'reset_interval' => 'none',
            'enable' => !$isExpired && !$isLimited,
            'status' => $status,
            'use_common_routing' => $user->use_common_routing,
            'is_sni_messenger' => $user->isSniMessenger(),
            'plan' => $plan ? [
                'name' => $plan->name,
                'duration' => $plan->duration,
                'duration_unit' => $plan->duration_unit,
                'is_demo' => $plan->is_demo,
            ] : null,
            'subscription' => $subscription ? [
                'plan_name' => $subscription->plan?->name ?? 'Unknown Plan',
                'is_active' => $subscription->isActive(),
                'is_expired' => $subscription->isExpired(),
                'started_at' => $subscription->start_date,
                'expires_at' => $subscription->end_date,
                'traffic_limit_bytes' => $trafficLimitBytes,
                'traffic_limit_gb' => $trafficLimitGb,
                'traffic_used_bytes' => $trafficUsedBytes,
                'traffic_used_gb' => $trafficUsedGb,
                'traffic_used_gb_in_fractions' => $trafficUsedGbInFractions,
                'traffic_usage_percentage' => $trafficUsagePercentage,
                'traffic_progress_color' => $trafficUsagePercentage > 90 ? 'red' : ($trafficUsagePercentage > 70 ? 'yellow' : 'green'),
                'remaining_formatted' => $remainingFormatted,
                'renewal_required' => $subscription->isRenewalRequired(),
                'is_about_to_expire' => $subscription->isAboutToExpire(),
                'is_demo' => $subscription->isDemo(),
            ] : null,
        ];
    }

    /**
     * Calculate remaining time with appropriate units.
     */
    private function calculateRemainingTime($expiryTime): array
    {
        if (!$expiryTime || $expiryTime->isPast()) {
            return [
                'value' => 0,
                'unit' => 'expired',
                'display' => 'Истекла',
                'display_en' => 'Expired'
            ];
        }

        $now = now();
        $diff = $now->diff($expiryTime);

        $totalHours = $diff->days * 24 + $diff->h;

        // If less than 1 hour, show minutes
        if ($totalHours < 1) {
            $minutes = $diff->i;
            return [
                'value' => $minutes,
                'unit' => 'minutes',
                'display' => $minutes . ' мин.',
                'display_en' => $minutes . ' min.'
            ];
        }

        // If less than 24 hours, show hours and minutes
        if ($diff->days < 1) {
            $hours = $totalHours;
            $minutes = $diff->i;

            if ($minutes > 0) {
                return [
                    'value' => $hours,
                    'unit' => 'hours',
                    'display' => $hours . ' ч. ' . $minutes . ' мин.',
                    'display_en' => $hours . ' h. ' . $minutes . ' min.'
                ];
            } else {
                return [
                    'value' => $hours,
                    'unit' => 'hours',
                    'display' => $hours . ' ч.',
                    'display_en' => $hours . ' h.'
                ];
            }
        }

        // Otherwise show days
        return [
            'value' => $diff->days,
            'unit' => 'days',
            'display' => $diff->days . ' дн.',
            'display_en' => $diff->days . ' days'
        ];
    }
}
