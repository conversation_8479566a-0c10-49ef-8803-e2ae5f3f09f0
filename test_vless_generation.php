<?php

require_once 'vendor/autoload.php';

// Test data similar to the JavaScript example
$streamSettings = [
    'network' => 'xhttp',
    'security' => 'tls',
    'settings' => [
        'encryption' => 'none'
    ],
    'xhttp' => [
        'path' => '/',
        'host' => 'gw4.smartvpn.vip',
        'mode' => 'auto'
    ],
    'tls' => [
        'settings' => [
            'fingerprint' => 'chrome'
        ],
        'alpn' => ['h2', 'http/1.1', 'h3'],
        'sni' => 'gw4.smartvpn.vip'
    ],
    'isTls' => true
];

// Create a mock AccessService to test the genVLESSLink method
class TestAccessService {
    private function getHeaderFromSettings(array $settings, string $headerName): string
    {
        $headers = $settings['headers'] ?? [];
        
        foreach ($headers as $header) {
            if (isset($header['name']) && strtolower($header['name']) === strtolower($headerName)) {
                return $header['value'] ?? '';
            }
        }
        
        return '';
    }

    public function genVLESSLink(array $stream, string $address = '', ?int $port = null, string $forceTls = 'same', string $remark = '', string $clientId = '', ?string $flow = null): string
    {
        $uuid = $clientId;
        $type = $stream['network'] ?? '';
        $security = $forceTls === 'same' ? ($stream['security'] ?? '') : $forceTls;
        $params = [];
        
        // Set basic parameters
        $params['type'] = $type;
        $params['encryption'] = $stream['settings']['encryption'] ?? 'none';
        
        // Handle different network types
        switch ($type) {
            case 'tcp':
                $tcp = $stream['tcp'] ?? [];
                if (($tcp['type'] ?? '') === 'http') {
                    $request = $tcp['request'] ?? [];
                    if (isset($request['path']) && is_array($request['path'])) {
                        $params['path'] = implode(',', $request['path']);
                    }
                    
                    // Find host header
                    $headers = $request['headers'] ?? [];
                    foreach ($headers as $header) {
                        if (isset($header['name']) && strtolower($header['name']) === 'host') {
                            $params['host'] = $header['value'] ?? '';
                            break;
                        }
                    }
                    $params['headerType'] = 'http';
                }
                break;
                
            case 'kcp':
                // KCP settings can be added here if needed
                break;
                
            case 'ws':
                $ws = $stream['ws'] ?? [];
                $params['path'] = $ws['path'] ?? '';
                $params['host'] = !empty($ws['host']) ? $ws['host'] : $this->getHeaderFromSettings($ws, 'host');
                break;
                
            case 'grpc':
                $grpc = $stream['grpc'] ?? [];
                $params['serviceName'] = $grpc['serviceName'] ?? '';
                $params['authority'] = $grpc['authority'] ?? '';
                if (!empty($grpc['multiMode'])) {
                    $params['mode'] = 'multi';
                }
                break;
                
            case 'httpupgrade':
                $httpupgrade = $stream['httpupgrade'] ?? [];
                $params['path'] = $httpupgrade['path'] ?? '';
                $params['host'] = !empty($httpupgrade['host']) ? $httpupgrade['host'] : $this->getHeaderFromSettings($httpupgrade, 'host');
                break;
                
            case 'xhttp':
                $xhttp = $stream['xhttp'] ?? [];
                $params['path'] = $xhttp['path'] ?? '';
                $params['host'] = !empty($xhttp['host']) ? $xhttp['host'] : $this->getHeaderFromSettings($xhttp, 'host');
                $params['mode'] = $xhttp['mode'] ?? '';
                break;
        }
        
        // Handle security settings
        if ($security === 'tls') {
            $params['security'] = 'tls';
            if (!empty($stream['isTls'])) {
                $tls = $stream['tls'] ?? [];
                $tlsSettings = $tls['settings'] ?? [];
                
                if (isset($tlsSettings['fingerprint'])) {
                    $params['fp'] = $tlsSettings['fingerprint'];
                }
                
                if (isset($tls['alpn']) && is_array($tls['alpn'])) {
                    $params['alpn'] = implode(',', $tls['alpn']);
                }
                
                if (!empty($tls['sni'])) {
                    $params['sni'] = $tls['sni'];
                }
                
                if (!empty($tlsSettings['echConfigList'])) {
                    $params['ech'] = $tlsSettings['echConfigList'];
                }
                
                if ($type === 'tcp' && !empty($flow)) {
                    $params['flow'] = $flow;
                }
            }
        } elseif ($security === 'reality') {
            $params['security'] = 'reality';
            $reality = $stream['reality'] ?? [];
            $realitySettings = $reality['settings'] ?? [];
            
            if (isset($realitySettings['publicKey'])) {
                $params['pbk'] = $realitySettings['publicKey'];
            }
            
            if (isset($realitySettings['fingerprint'])) {
                $params['fp'] = $realitySettings['fingerprint'];
            }
            
            if (!empty($reality['serverNames'])) {
                $serverNames = is_string($reality['serverNames']) ? 
                    explode(',', $reality['serverNames']) : 
                    $reality['serverNames'];
                if (!empty($serverNames)) {
                    $params['sni'] = trim($serverNames[0]);
                }
            }
            
            if (!empty($reality['shortIds'])) {
                $shortIds = is_string($reality['shortIds']) ? 
                    explode(',', $reality['shortIds']) : 
                    $reality['shortIds'];
                if (!empty($shortIds)) {
                    $params['sid'] = trim($shortIds[0]);
                }
            }
            
            if (!empty($realitySettings['spiderX'])) {
                $params['spx'] = $realitySettings['spiderX'];
            }
            
            if (!empty($realitySettings['mldsa65Verify'])) {
                $params['pqv'] = $realitySettings['mldsa65Verify'];
            }
            
            if ($type === 'tcp' && !empty($flow)) {
                $params['flow'] = $flow;
            }
        } else {
            $params['security'] = 'none';
        }
        
        // Build the VLESS URL
        $link = "vless://{$uuid}@{$address}:{$port}";
        
        // Add query parameters
        $queryString = http_build_query(array_filter($params, function($value) {
            return $value !== '' && $value !== null;
        }));
        
        if (!empty($queryString)) {
            $link .= '?' . $queryString;
        }
        
        // Add remark as fragment
        if (!empty($remark)) {
            $link .= '#' . urlencode($remark);
        }
        
        return $link;
    }
}

// Test the function
$testService = new TestAccessService();
$result = $testService->genVLESSLink(
    stream: $streamSettings,
    address: 'gw4.smartvpn.vip',
    port: 8443,
    forceTls: 'same',
    remark: 'Test Connection',
    clientId: 'e8231f81-945b-4f8b-b90d-43666dea3217',
    flow: null
);

echo "Generated VLESS URL:\n";
echo $result . "\n\n";

// Expected result from JavaScript:
echo "Expected result:\n";
echo "vless://<EMAIL>:8443?type=xhttp&encryption=none&path=%2F&host=gw4.smartvpn.vip&mode=auto&security=tls&fp=chrome&alpn=h2%2Chttp%2F1.1%2Ch3&sni=gw4.smartvpn.vip#Test%20Connection\n";
